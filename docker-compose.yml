version : '3'
services :
  web :
    container_name: hotelinking_api_server
    build :
      context : ./
      dockerfile : web.docker
    volumes :
      - ./:/var/www
    ports:
      - "${DOCKER_APP_PORT}:80"
    links:
      - app
    depends_on:
      - app
  app :
    container_name: hotelinking_api_app
    build :
      context : ./
      dockerfile : api.docker
    volumes :
          - ./:/var/www
    #network_mode: "host"
