<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddOpenQuestionEnumOnBrandQuestionTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        DB::statement("ALTER TABLE question_brand MODIFY COLUMN type ENUM('Rating', 'Multiresponse', 'Open Question') NOT NULL");

    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        DB::statement("ALTER TABLE question_brand MODIFY COLUMN type ENUM('Rating', 'Multiresponse') NOT NULL");
    }
}
