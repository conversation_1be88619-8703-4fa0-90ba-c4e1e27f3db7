<?php

namespace Tests\Feature;

use Illuminate\Support\Facades\Artisan;
use Tests\TestCase;

class HotelSeederCommandTest extends TestCase
{
    public function testHotelWithIntegrationCreated()
    {
        Artisan::call('hotelinking:hotel', [
            'email' => '<EMAIL>',
            'integration' => true,
        ]);

        $this->assertDatabaseHas('hoteles', [
            'email' => '<EMAIL>'
        ]);

        $this->assertDatabaseHas('hotel_wifi_integrations', [
            'wifi_id' => '9'
        ]);
    }

    public function testHotelWithoutIntegrationCreated()
    {
        Artisan::call('hotelinking:hotel', [
            'email' => '<EMAIL>',
            'integration' => false,
        ]);

        $this->assertDatabaseHas('hoteles', [
            'email' => '<EMAIL>'
        ]);

        $this->assertDatabaseMissing('hotel_wifi_integrations', [
            'wifi_id' => '9'
        ]);
    }
}
