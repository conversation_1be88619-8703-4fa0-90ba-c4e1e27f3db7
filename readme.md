# HOTELINKING API

The Hotelinking Api is used to supply a communication with the Hotelinking app functionalities.

First of all it is recommended to download and install this project on your premises following the general setup instructions defined in our [Confluence](https://hotelinking.atlassian.net/wiki/spaces/MA/pages/2272264235/Configuracion+Initial+Hotelinking+-+2).

To view the documentation, run the following command:

```sh
npm run docs:serve
```
