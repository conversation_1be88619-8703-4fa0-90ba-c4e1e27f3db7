<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link
      rel="icon"
      type="image/x-icon"
      href="https://hotelinking.com/favicon.ico"
    />
    <link
      href="https://unpkg.com/prismjs@1.20.0/themes/prism-okaidia.css"
      rel="stylesheet"
    />
    <title>{{ title }}</title>
    <style>
      html,
      body {
        margin: 0;
        padding: 0;
        height: 100%;
      }

      .header-anchor {
        font-size: 80%;
      }

      .grid {
        font-family: system-ui;
        display: grid;
        grid-template-columns: 300px minmax(0, 1fr);
        margin: auto;
        padding: 0 30px;
        height: 100%;
      }

      iframe {
        border: none;
      }

      #toggle {
        display: none;
      }

      #toggle:hover {
        cursor: pointer;
      }

      #nav {
        grid-column: 1;
        padding-inline-start: 0px;
        list-style-type: none;
        padding-right: 20px;
        margin: 3px 0;
        position: fixed;
      }

      #nav ul {
        list-style-type: none;
      }

      #nav > ul > *:first-child {
        line-height: 1;
        font-size: 30px;
        font-weight: bold;
      }

      #nav ul li ul {
        margin-left: -25px
      }

      #content {
        grid-column: 2;
        padding-left: 20px;
        border-left: 1px solid #ddd;
        margin: 10px 0;
      }

      #content > *:first-child {
        margin: 0;
      }

      .active {
        font-weight: bold;
      }

      a {
        text-decoration: none;
        white-space: nowrap;
        color: #333;
      }

      h1,
      h2,
      h3 {
        margin-bottom: 0.5em;
      }

      p {
        margin-top: 0.5em;
        line-height: 1.5;
      }

      li {
        line-height: 1.5;
      }

      li.__0 {
        padding-left: 0;
      }

      li.__1 {
        padding-left: 0;
      }

      li.__2 {
        padding-left: 0;
      }

      li.__3 {
        padding-left: 20px;
      }

      li.__4 {
        padding-left: 40px;
      }

      pre {
        display: block;
        font-family: monospace;
        white-space: pre;
        margin: 1em 2px;
        padding: 15px;
        box-shadow: 0 0 2px #555;
        overflow-x: auto;
        background-color: #f0f0f0;
      }

      code {
        font-family: monospace;
        background-color: #f0f0f0;
      }

      *:not(pre) > code {
        padding: 5px;
      }

      #content a {
        color: #090;
      }

      #content img {
        max-width: 100%;
        display: block;
        margin: 0 auto;
      }

      @media only screen and (max-width: 700px) {
        .grid {
          grid-template-columns: 0 minmax(0, 1fr);
          padding: 0 0 0 40px;
        }

        #toggle {
          display: block;
          position: fixed;
          top: 10px;
          left: 10px;
          width: 20px;
          height: 30px;
          background: repeating-linear-gradient(#ddd, #fff 10px);
        }

        #nav {
          display: none;
        }

        #content {
          border-left: none;
          padding: 0 20px 0 1px;
        }
      }

      .show_nav {
        grid-template-columns: 1fr 2fr;
      }

      .show_nav > #nav {
        display: block;
      }

      .show_nav > #content * {
        font-size: 30%;
        padding: 0;
        overflow: hidden;
      }

      table {
        border-collapse: collapse;
        margin: 25px 0;
        font-size: 0.9em;
        font-family: sans-serif;
        min-width: 400px;
        box-shadow: 0 0 20px rgba(0, 0, 0, 0.15);
      }

      table thead tr {
        background-color: #ccc;
        text-align: center;
      }

      table th,
      table td {
        padding: 12px 15px;
      }

      table tbody tr {
        border-bottom: 1px solid #dddddd;
      }

      table tbody tr:nth-of-type(even) {
        background-color: #f3f3f3;
      }
      
    </style>
    {% mermaid_js %}
  </head>

  <body>
    <div class="grid">
      <div id="toggle"></div>

      <div id="nav">
        {{ collections.docs | eleventyNavigation | eleventyNavigationToHtml }}
      </div>

      <div id="content">
        {{ content }}
      </div>
    </div>
  </body>

  <script>
    const toggle = document.getElementById("toggle");

    toggle.onclick = (e) => {
      e.target.parentElement.classList.toggle("show_nav");
    };
  </script>
</html>
