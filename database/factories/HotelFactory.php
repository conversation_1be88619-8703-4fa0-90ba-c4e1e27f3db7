<?php

use Faker\Generator as Faker;

/**
 * Created by <PERSON><PERSON><PERSON>tor<PERSON>.
 * User: Ricardo
 * Date: 19/01/2018
 * Time: 10:43
 */
$factory->define(App\Hotel::class, function (Faker $faker) {
    $email = $faker->unique()->safeEmail;
    $awsImageUrls = [
        'http://dev-images.hotelinking.com/brands/00b20c71-8c1d-429a-b19e-75f52b79d38d/images/logo/original.jpg',
        'https://dev-images.hotelinking.com/brands/00f1c56c-24ac-4c86-930a-df4057a36863/images/logo/original-1642068913135.jpg',
        'https://dev-images.hotelinking.com/brands/014533f4-78b0-4bc7-b065-08530eb35e42/images/logo/original-1698135720991.jpg',
        'https://dev-images.hotelinking.com/brands/0630dfae-719c-418f-8f5a-de44a40e8aee/images/logo/medium.jpg',
        'https://dev-images.hotelinking.com/brands/09bd4e3a-3601-4339-8478-937ad6c1188c/images/logo/original-1641216995363.jpg',
        'https://dev-images.hotelinking.com/brands/0a537363-b422-4db7-8ae4-b0647b9dfd33/images/logo/medium-1703847533580.jpg',
        'https://dev-images.hotelinking.com/brands/0f8f097a-9b08-4923-a0bd-6c5ec08dd031/images/logo/original-1573730123903.jpg',
        'https://dev-images.hotelinking.com/brands/12ba0737-1594-4b4c-a025-d581981b3491/images/logo/original-1695891691775.jpg',
        'https://dev-images.hotelinking.com/brands/135ab7d5-69d1-47f6-a73f-453adb8b13db/images/logo/original.jpg',
        'https://dev-images.hotelinking.com/brands/f4a8c16e-a4c9-4a84-91e0-274e667513e6/images/logo/original-1648551505287.jpg',        
    ];
    return [
        'logo'                 => $faker->randomElement($awsImageUrls),
        'email'                => $email,
        'password'             => '3d816a4200a01ed808182fbc55e2ed62e2506da9',
        'hotelName'            => substr($faker->company, 0, 100),
        'territory'            => '',
        'street'               => $faker->randomElement(['221B Baker Street', 'Elm Street nº 666', '112 de Ocean Avenue']),
        'country'              => '',
        'city'                 => $faker->randomElement(['London, UK', 'Nueva York, USA']),
        'place_name'           => $faker->randomElement(['UK', 'USA']),
        'place_country'        => '',
        'place_adm_area'       => '',
        'lat'                  => '35.0',
        'lng'                  => '36.0',
        'place_id'             => '',
        'cp'                   => '',
        'website'              => $faker->url(),
        'emailReserva'         => $email,
        'telefonoReservas'     => '123',
        'name'                 => substr($faker->name, 0, 50),
        'apellidos'            => substr($faker->name, 0, 50),
        'telefono'             => '123',
        'cargo'                => '',
        'estrellas'            => $faker->numberBetween($min = 1, $max = 10),
        'min_rango'            => '0',
        'max_rango'            => '0',
        'n_habitaciones'       => $faker->numberBetween($min = 50, $max = 1000),
        'tipo_hotel'           => '',
        'decoracion'           => '',
        'descripcion'          => 'elemental...',
        'condiciones'          => '',
        'notif'                => '0',
        'review'               => '1',
        'diasEnvioReview'      => '1',
        'alert_friends'        => '0',
        'verificado'           => '1',
        'modal'                => '0',
        'alert'                => '0',
        'fotoBg'               => '0',
        'jan'                  => '0',
        'feb'                  => '0',
        'mar'                  => '0',
        'apr'                  => '0',
        'may'                  => '0',
        'jun'                  => '0',
        'jul'                  => '0',
        'ago'                  => '0',
        'sep'                  => '0',
        'oct'                  => '0',
        'nov'                  => '0',
        'dece'                 => '0',
        'rating'               => '5',
        'limitGuest'           => '0',
        'quota'                => '500',
        'moneda'               => $faker->randomElement(['EUR', 'USD']),
        'lang'                 => $faker->randomElement(['es', 'en']),
        'twitterAccount'       => substr($faker->name, 0, 50),
        'booking_engine'       => '5',
        'iframe'               => 'disable',
        'iframe_style'         => '1',
        'landing_iframe'       => 'disable',
        'landing_iframe_style' => '0',
        'facebook_page'        => 'hotelinking',
        'sending_email'        => $email,
        'stay_time'            => 7,
        'time_zone_id'         => $faker->numberBetween($min = 1, $max = 141),
        //        'loyalty_min_visits'=>'2',
        //        'loyalty_emails'=>$faker->unique()->safeEmail,
        //        'loyalty_alerts'=>'1',
        //        'birthdayAlertEmails'=>$faker->unique()->safeEmail,
    ];
});
