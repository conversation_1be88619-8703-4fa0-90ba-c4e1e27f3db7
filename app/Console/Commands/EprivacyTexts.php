<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Artisan;

class EprivacyTexts extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'hotelinking:eprivacy-texts';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Seed or update eprivacy texts.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        Artisan::call('db:seed', [
            '--class' => 'EprivacyCustomContentSeeder'
        ]);

        Artisan::call('db:seed', [
            '--class' => 'TreatmentProtocolSeeder'
        ]);
    }
}
