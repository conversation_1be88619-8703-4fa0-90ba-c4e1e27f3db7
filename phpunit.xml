<?xml version="1.0" encoding="UTF-8"?>
<phpunit xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:noNamespaceSchemaLocation="./vendor/phpunit/phpunit/phpunit.xsd"
         backupGlobals="false"
         backupStaticAttributes="false"
         bootstrap="vendor/autoload.php"
         colors="true"
         convertErrorsToExceptions="true"
         convertNoticesToExceptions="true"
         convertWarningsToExceptions="true"
         processIsolation="false"
         stopOnFailure="false">
    <testsuites>
        <testsuite name="Feature">
            <directory suffix="Test.php">./tests/Feature</directory>
        </testsuite>

        <testsuite name="Unit">
            <directory suffix="Test.php">./tests/Unit</directory>
        </testsuite>
    </testsuites>
    <filter>
        <whitelist processUncoveredFilesFromWhitelist="true">
            <directory suffix=".php">./app</directory>
        </whitelist>
    </filter>
    <php>
        <env name="APP_ENV" value="testing"/>
        <env name="CACHE_DRIVER" value="array"/>
        <env name="SESSION_DRIVER" value="array"/>
        <env name="QUEUE_DRIVER" value="sync"/>
        <env name="DB_CONNECTION" value="test"/>
        <env name="AWS_GATEWAY_ID" value="internal"/>
        <env name="EXTERNAL_API_ID" value="external"/>
        <ini name="display_errors" value="On"/>
        <ini name="display_startup_errors" value="On"/>
    </php>
    <logging>
        <log type="coverage-html" target="./report/html-coverage-unit" lowUpperBound="50" highLowerBound="80"/>
        <log type="coverage-php" target="./report/clover-unit.cov"/>
        <log type="junit" target="./report/junit.xml"/>
        <log type="coverage-text" target="php://stdout" showUncoveredFiles="false"/>
        <log type="testdox-html" target="./report/testdox.html"/>
        <log type="testdox-text" target="./report/testdox.txt"/>
    </logging>
</phpunit>
