<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class OfertaReferralToken extends Model
{
    protected $table = 'oferta_referral_token';
    public $timestamps = false;

    public function userCupones()
    {
        return $this->hasOne('App\UserCupones', 'id', 'id_cupon');
    }

    public function shareTypes()
    {
        return $this->belongsTo('App\ShareTypes', 'id_tipo_share');
    }

    public function hotel()
    {
        return $this->belongsTo('App\Hotel', 'id_hotel');
    }
}
