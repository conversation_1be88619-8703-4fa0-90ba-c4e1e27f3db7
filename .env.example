APP_NAME=hlapi
APP_ENV=local
APP_KEY=base64:/ovAXyzJv60OZCG8+sQ4E+D6BTn1LAczRXWN4arCz/0=
APP_DEBUG=true
APP_LOG_LEVEL=debug
APP_URL=http://local-api.hotelinking.com

HOTELINKING_DOMAIN = hlapi/api

DB_SCHEMA_FILE=/sql/hotelinking_schema_v1.sql
DB_FUNCTIONS_FILE=/sql/hotelinking_functions_v1.sql

DB_CONNECTION=mysql
DB_HOST=hlmysql8
DB_HOST_READ=hlmysql8
DB_HOST_WRITE=hlmysql8
DB_PORT=3306
DB_DATABASE=hotelinking_app_local
DB_USERNAME=root
DB_PASSWORD=secret

DB_HOST_TEST=hlmysql8
DB_PORT_TEST=3306
DB_DATABASE_TEST=hotelinking_app_test
DB_USERNAME_TEST=root
DB_PASSWORD_TEST=secret

BROADCAST_DRIVER=log
CACHE_DRIVER=redis
SESSION_DRIVER=file
SESSION_LIFETIME=120
QUEUE_DRIVER=database
QUEUE_CONNECTION=database

CACHE_PREFIX=HL_API_
REDIS_HOST=redis
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=smtp
MAIL_HOST=smtp.mailtrap.io
MAIL_PORT=2525
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS=null
MAIL_FROM_NAME="${APP_NAME}"

SQS_PREFIX = https://sqs.eu-west-1.amazonaws.com/060632736596
SQS_QUEUE = dev-queue-hotelinking-api
SQS_REGION=eu-west-1

AWS_KEY=
AWS_SECRET=
AWS_REGION=eu-west-1
AWS_DEFAULT_REGION=eu-west-1
AWS_EVENT_BUS_NAME=dev-eventbus

AWS_COGNITO_CLIENT_ID=
AWS_COGNITO_CLIENT_SECRET=
AWS_COGNITO_USER_POOL_ID=
AWS_COGNITO_SUITE_CLIENT_ID=g2o5jlsc7p390ihg7f3eo3gad
AWS_COGNITO_SUITE_CLIENT_SECRET=14rel9md8g2rpega6gqdkh5r8kg5r8bn37of0sc3h8ffi16kf90m
AWS_COGNITO_SUITE_USER_POOL_ID=eu-west-1_8vstpVubc
AWS_GATEWAY_ID=
AWS_GATEWAY_URL_ID=
AWS_REPORTS_BUCKET=dev-reports.hotelinking.com
GATEWAY_URL=https://s4e89eysz1-api.eu-west-1.amazonaws.com/dev/
AWS_GATEWAY_URL=https://s4e89eysz1-api.eu-west-1.amazonaws.com/dev/
SCHEMAS_TABLE=dev-eventSchemas
STREAM_SUB_DOMAIN=streams/
AWS_GATEWAY_STREAM=streams/
HOTELINKING_URL=http://localhost/
AUTOCHECKIN_REDIRECT_URL=https://dev-autocheckin.hotelinking.com/
BOUNCER_URL=http://localhost/
EVENT_BUS_NAME=dev-eventbus
EVENT_BUS_SOURCE_NAME='hl.api'

EXTERNAL_API_ID=
EXTERNAL_API_URL=http://localhost

SURVEYS_AVATAR_PLACEHOLDER=http://images.hotelinking.com/default/avatar.png
SURVEYS_URL=http://localhost:8080

CLOUDWATCH_LOG_ENABLED=false
CLOUDWATCH_LOG_NAME=laravel

CLOUDWATCH_LOG_STREAM_NAME=dev-hotelinking-api
CLOUDWATCH_LOG_GROUP_NAME=dev-laravel

# Factory thresholds
CHAIN_FACTORY_THRESHOLD=3
CHAIN_HOTEL_FACTORY_THRESHOLD=2
CHAIN_HOTEL_USER_FACTORY_THRESHOLD=15
INDIPENDENT_HOTEL_FACTORY_THRESHOLD=2
INDIPENDENT_HOTEL_USER_FACTORY_THRESHOLD=15000

PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_APP_CLUSTER=mt1

MIX_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
MIX_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"
