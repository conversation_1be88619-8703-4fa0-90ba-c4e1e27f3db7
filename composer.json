{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "repositories": [{"type": "vcs", "url": "*****************:hotelinking_team/libs-services.git"}], "require": {"php": "^7.2.5", "ext-json": "^1.6", "aws/aws-php-sns-message-validator": "^1.6", "doctrine/dbal": "^2.10", "aws/aws-sdk-php": "^3.158", "dusterio/laravel-plain-sqs": "^0.1.21", "exussum12/coverage-checker": "^0.11.2", "fideloper/proxy": "^4.2", "fruitcake/laravel-cors": "^2.0", "guzzlehttp/guzzle": "^6.3", "hotelinking/libs-services": "dev-master", "justinrainbow/json-schema": "^5.2", "laravel/framework": "^7.0", "laravel/tinker": "^2.0", "league/csv": "^9.6", "league/flysystem-aws-s3-v3": "^1.0", "league/iso3166": "^2.1", "maxbanton/cwh": "^2.0", "pmill/aws-cognito": "^0.2.11", "ua-parser/uap-php": "^3.9", "yadakhov/insert-on-duplicate-key": "^1.2"}, "require-dev": {"beyondcode/laravel-dump-server": "^1.4", "facade/ignition": "^2.0", "fzaninotto/faker": "^1.9.1", "mockery/mockery": "^1.3.1", "nunomaduro/collision": "^4.1", "phpunit/phpunit": "^8.5", "squizlabs/php_codesniffer": "3.4.0"}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true}, "extra": {"laravel": {"dont-discover": []}}, "autoload": {"psr-4": {"App\\": "app/"}, "files": ["app/Helpers/countries.php"], "classmap": ["database/seeds", "database/factories"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "minimum-stability": "dev", "prefer-stable": true, "scripts": {"psr": ["@psr-summary"], "psr-summary": ["vendor/bin/phpcs . --standard=PSR1,PSR12 --ignore=vendor,bootstrap/cache,database,storage --report=summary --extensions=php --tab-width=4 -n"], "psr-code": ["vendor/bin/phpcs . --standard=PSR1,PSR12 --ignore=vendor,bootstrap/cache,database,storage --report=code --extensions=php --tab-width=4 -n"], "psr-autofix": ["vendor/bin/phpcbf . --standard=PSR1,PSR12 --ignore=vendor,bootstrap/cache,database,storage --report=diff --extensions=php --tab-width=4 -n"], "test": ["php artisan config:cache", "php artisan config:clear", "php artisan migrate --seed --database=test", "./vendor/bin/phpunit --verbose --coverage-html report/"], "docker-test": ["docker exec hlapi php artisan migrate:refresh --database=test --seed", "docker exec hlapi composer test"], "post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi", "cp pre-commit.example .git/hooks/pre-commit", "chmod +x .git/hooks/pre-commit"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}}