APP_NAME=Laravel
APP_ENV=local
APP_KEY=base64:
APP_DEBUG=true
APP_LOG_LEVEL=debug
APP_URL=http://localhost

DEV_DB_HOST=**************
DEV_DB_PORT=3306
DEV_DB_DATABASE=hotelinking_app_dev
DEV_DB_USERNAME=bitbucket
DEV_DB_PASSWORD=Hotelinking2610772326

BETA_DB_HOST=z30hotelinkingdbbeta01.c1yil9ynqbgr.eu-west-1.rds.amazonaws.com
BETA_DB_PORT=3306
BETA_DB_DATABASE=hotelinkingbeta
BETA_DB_USERNAME=hotelinkingbeta
BETA_DB_PASSWORD=dpFQ#s0H5T$k6I9z

TESTING_DB_HOST=**************
TESTING_DB_PORT=3306
TESTING_DB_DATABASE=hotelinking_migration_test
TESTING_DB_USERNAME=bitbucket
TESTING_DB_PASSWORD=Hotelinking2610772326

DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=hotelinking_migration_test
DB_USERNAME=root
DB_PASSWORD=superhotelinking

BROADCAST_DRIVER=log
CACHE_DRIVER=file
SESSION_DRIVER=file
SESSION_LIFETIME=120
QUEUE_DRIVER=sync

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_DRIVER=smtp
MAIL_HOST=smtp.mailtrap.io
MAIL_PORT=2525
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null

PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_APP_CLUSTER=mt1
