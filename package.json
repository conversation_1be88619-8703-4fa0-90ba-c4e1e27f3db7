{"private": true, "scripts": {"docs:build": "npx rimraf docs && npx eleventy", "docs:serve": "npx eleventy --serve", "dev": "npm run development", "development": "cross-env NODE_ENV=development node_modules/webpack/bin/webpack.js --progress --hide-modules --config=node_modules/laravel-mix/setup/webpack.config.js", "watch": "npm run development -- --watch", "watch-poll": "npm run watch -- --watch-poll", "hot": "cross-env NODE_ENV=development node_modules/webpack-dev-server/bin/webpack-dev-server.js --inline --hot --config=node_modules/laravel-mix/setup/webpack.config.js", "prod": "npm run production", "production": "cross-env NODE_ENV=production node_modules/webpack/bin/webpack.js --no-progress --hide-modules --config=node_modules/laravel-mix/setup/webpack.config.js"}, "devDependencies": {"@11ty/eleventy": "^1.0.2", "@11ty/eleventy-navigation": "^0.3.5", "@11ty/eleventy-plugin-syntaxhighlight": "^4.1.0", "@kevingimbel/eleventy-plugin-mermaid": "^2.0.0", "axios": "^0.19.2", "bootstrap-sass": "^3.3.7", "cross-env": "^7.0", "jquery": "^3.2", "laravel-mix": "^5.0.1", "laravel-mix-purgecss": "^5.0.0", "lodash": "^4.17.19", "resolve-url-loader": "^3.1.0", "markdown-it-anchor": "^8.6.5", "sass": "^1.26.10", "sass-loader": "^8.0.2", "vue": "^2.5.7", "vue-template-compiler": "^2.6.11"}, "dependencies": {"tailwindcss": "^2.0.2"}}