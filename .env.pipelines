APP_NAME="Hotelinking API"
APP_ENV=testing
APP_KEY=
APP_DEBUG=true
APP_LOG_LEVEL=debug
APP_URL=http://dev-api.hotelinking.com

AWS_KEY=********************
AWS_SECRET=vUi4ofXq4mpgyHMBl1gT7n4eV6iCh5Mjv3wT+G49
GATEWAY_URL=https://s4e89eysz1.execute-api.eu-west-1.amazonaws.com/
AWS_GATEWAY_URL=https://s4e89eysz1.execute-api.eu-west-1.amazonaws.com/
SCHEMAS_TABLE=dev-eventSchemas
STREAM_SUB_DOMAIN=streams
AWS_GATEWAY_STREAM=streams
SURVEYS_URL=https://test.com

BROADCAST_DRIVER=log
CACHE_DRIVER=file
SESSION_DRIVER=file
SESSION_LIFETIME=120
QUEUE_DRIVER=sync

#Dev Laravel IAM
AWS_REGION=eu-west-1
AWS_DEFAULT_REGION=eu-west-1
HOTELINKING_URL=http://localhost/

SURVEYS_URL=https://test.com
SCHEMAS_TABLE=dev-eventSchemas